import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SurveyData } from '@/utils/dataManager';

interface SurveyResponseFormProps {
  survey: SurveyData;
  onSubmit: (responses: { [questionId: string]: any }) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const SurveyResponseForm: React.FC<SurveyResponseFormProps> = ({
  survey,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [responses, setResponses] = useState<{ [questionId: string]: any }>({});
  const [errors, setErrors] = useState<{ [questionId: string]: string }>({});

  const handleResponseChange = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value
    }));
    
    // Clear error when user provides a response
    if (errors[questionId]) {
      setErrors(prev => ({
        ...prev,
        [questionId]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: { [questionId: string]: string } = {};
    
    survey.questions.forEach(question => {
      if (question.required && (!responses[question.id] || responses[question.id] === '')) {
        newErrors[question.id] = 'Pertanyaan ini wajib dijawab';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(responses);
    }
  };

  const renderQuestion = (question: any) => {
    const questionId = question.id;
    const hasError = errors[questionId];

    switch (question.type) {
      case 'text':
        return (
          <div key={questionId} className="space-y-2">
            <Label htmlFor={questionId} className="text-sm font-medium">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={questionId}
              value={responses[questionId] || ''}
              onChange={(e) => handleResponseChange(questionId, e.target.value)}
              placeholder="Masukkan jawaban Anda..."
              className={hasError ? 'border-red-500' : ''}
            />
            {hasError && <p className="text-red-500 text-sm">{errors[questionId]}</p>}
          </div>
        );

      case 'textarea':
        return (
          <div key={questionId} className="space-y-2">
            <Label htmlFor={questionId} className="text-sm font-medium">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={questionId}
              value={responses[questionId] || ''}
              onChange={(e) => handleResponseChange(questionId, e.target.value)}
              placeholder="Masukkan jawaban Anda..."
              rows={4}
              className={hasError ? 'border-red-500' : ''}
            />
            {hasError && <p className="text-red-500 text-sm">{errors[questionId]}</p>}
          </div>
        );

      case 'radio':
        return (
          <div key={questionId} className="space-y-3">
            <Label className="text-sm font-medium">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <RadioGroup
              value={responses[questionId] || ''}
              onValueChange={(value) => handleResponseChange(questionId, value)}
              className={hasError ? 'border border-red-500 rounded p-2' : ''}
            >
              {question.options?.map((option: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${questionId}-${index}`} />
                  <Label htmlFor={`${questionId}-${index}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            {hasError && <p className="text-red-500 text-sm">{errors[questionId]}</p>}
          </div>
        );

      case 'checkbox':
        return (
          <div key={questionId} className="space-y-3">
            <Label className="text-sm font-medium">
              {question.text}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className={`space-y-2 ${hasError ? 'border border-red-500 rounded p-2' : ''}`}>
              {question.options?.map((option: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${questionId}-${index}`}
                    checked={(responses[questionId] || []).includes(option)}
                    onCheckedChange={(checked) => {
                      const currentValues = responses[questionId] || [];
                      if (checked) {
                        handleResponseChange(questionId, [...currentValues, option]);
                      } else {
                        handleResponseChange(questionId, currentValues.filter((v: string) => v !== option));
                      }
                    }}
                  />
                  <Label htmlFor={`${questionId}-${index}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </div>
            {hasError && <p className="text-red-500 text-sm">{errors[questionId]}</p>}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{survey.title}</CardTitle>
        {survey.description && (
          <p className="text-gray-600">{survey.description}</p>
        )}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {survey.questions.map(renderQuestion)}
          
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" className="university-gradient" disabled={isLoading}>
              {isLoading ? 'Menyimpan...' : 'Kirim Jawaban'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default SurveyResponseForm;
