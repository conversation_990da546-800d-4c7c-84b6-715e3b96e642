import React, { useState, useEffect } from 'react';
import { Search, Plus, Filter, Eye, Edit, Trash2, Upload, Download, FileSpreadsheet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Layout from '@/components/Layout';
import AlumniForm from '@/components/forms/AlumniForm';
import { dataManager, AlumniData } from '@/utils/dataManager';
import { sqliteDataManager } from '@/utils/sqliteDataManager';
import { generateExcelTemplate, parseExcelFile, getAlumniTemplateConfig } from '@/utils/excelUtils';

const AlumniList = () => {
  const [alumni, setAlumni] = useState<AlumniData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedAlumni, setSelectedAlumni] = useState<AlumniData | null>(null);

  // Import states
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);

  useEffect(() => {
    loadAlumni();
  }, []);

  const loadAlumni = async () => {
    setIsLoading(true);
    try {
      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let data;

      if (isServerAvailable) {
        data = await sqliteDataManager.getAlumniData();
      } else {
        data = dataManager.getAlumniData();
      }

      setAlumni(data);
    } catch (error) {
      console.error('Failed to load alumni:', error);
      // Fallback to localStorage
      const data = dataManager.getAlumniData();
      setAlumni(data);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddAlumni = () => {
    setSelectedAlumni(null);
    setIsFormOpen(true);
  };

  const handleViewAlumni = (alumni: AlumniData) => {
    setSelectedAlumni(alumni);
    setIsViewDialogOpen(true);
  };

  const handleEditAlumni = (alumni: AlumniData) => {
    setSelectedAlumni(alumni);
    setIsFormOpen(true);
  };

  const handleDeleteAlumni = (alumni: AlumniData) => {
    setSelectedAlumni(alumni);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (formData: any) => {
    setIsLoading(true);
    try {
      // Map form data to AlumniData interface
      const alumniData: Omit<AlumniData, 'id' | 'createdAt' | 'updatedAt'> = {
        userId: selectedAlumni?.userId || undefined, // Don't generate fake user_id
        nim: formData.nim || '',
        namaLengkap: formData.name || '',
        programStudi: formData.program || '',
        fakultas: formData.faculty || '',
        tahunMasuk: (formData.graduationYear || new Date().getFullYear()) - 4,
        tahunLulus: formData.graduationYear || new Date().getFullYear(),
        ipk: 3.5, // Default value
        email: formData.email || '',
        noTelepon: formData.phone || '',
        alamat: formData.address || '',
        statusVerifikasi: formData.status || 'pending'
      };

      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();

      if (isServerAvailable) {
        if (selectedAlumni) {
          // Update existing alumni
          await sqliteDataManager.updateAlumni(selectedAlumni.id, alumniData);
        } else {
          // Add new alumni
          await sqliteDataManager.addAlumni(alumniData);
        }
      } else {
        if (selectedAlumni) {
          // Update existing alumni
          dataManager.updateAlumni(selectedAlumni.id, alumniData);
        } else {
          // Add new alumni
          dataManager.addAlumni(alumniData);
        }
      }
      loadAlumni();
      setIsFormOpen(false);
      setSelectedAlumni(null);
    } catch (error) {
      console.error('Error saving alumni:', error);
      alert('Gagal menyimpan data alumni');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedAlumni) {
      setIsLoading(true);
      try {
        // Try SQLite first, fallback to localStorage
        const isServerAvailable = await sqliteDataManager.isServerAvailable();

        if (isServerAvailable) {
          await sqliteDataManager.deleteAlumni(selectedAlumni.id);
        } else {
          dataManager.deleteAlumni(selectedAlumni.id);
        }

        loadAlumni();
        setIsDeleteDialogOpen(false);
        setSelectedAlumni(null);
      } catch (error) {
        console.error('Error deleting alumni:', error);
        alert('Gagal menghapus data alumni');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Import functions
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        alert('Ukuran file terlalu besar. Maksimal 10MB.');
        return;
      }

      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv',
        'application/json'
      ];

      if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv|json)$/i)) {
        alert('Format file tidak didukung. Gunakan Excel (.xlsx, .xls), CSV, atau JSON.');
        return;
      }

      setImportFile(file);
    }
  };

  const downloadTemplate = () => {
    const config = getAlumniTemplateConfig();
    generateExcelTemplate(config);
  };

  const handleImport = async () => {
    if (!importFile) return;

    setIsImporting(true);
    setImportProgress(0);

    try {
      let importData: any[] = [];

      // Parse file based on type
      if (importFile.name.endsWith('.json')) {
        const fileContent = await readFileContent(importFile);
        const jsonData = JSON.parse(fileContent);
        importData = Array.isArray(jsonData) ? jsonData : jsonData.alumni || [];
      } else if (importFile.name.endsWith('.csv')) {
        const fileContent = await readFileContent(importFile);
        importData = parseCSV(fileContent);
      } else if (importFile.name.endsWith('.xlsx') || importFile.name.endsWith('.xls')) {
        // Parse Excel files
        importData = await parseExcelFile(importFile);
      } else {
        throw new Error('Format file tidak didukung. Gunakan .xlsx, .csv, atau .json');
      }

      if (importData.length === 0) {
        alert('File tidak berisi data yang valid.');
        return;
      }

      // Validate and import data
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (let i = 0; i < importData.length; i++) {
        const item = importData[i];
        setImportProgress(Math.round(((i + 1) / importData.length) * 100));

        try {
          // Validate required fields
          if (!item.namaLengkap || !item.nim) {
            errors.push(`Baris ${i + 1}: Nama lengkap dan NIM wajib diisi`);
            errorCount++;
            continue;
          }

          // Check for duplicate NIM
          const existingAlumni = alumni.find(a => a.nim === item.nim);
          if (existingAlumni) {
            errors.push(`Baris ${i + 1}: NIM ${item.nim} sudah ada`);
            errorCount++;
            continue;
          }

          // Create alumni data
          const alumniData: AlumniData = {
            id: `alumni-${Date.now()}-${i}`,
            namaLengkap: item.namaLengkap,
            nim: item.nim,
            programStudi: item.programStudi || '',
            fakultas: item.fakultas || '',
            tahunLulus: parseInt(item.tahunLulus) || new Date().getFullYear(),
            email: item.email || '',
            noTelepon: item.noTelepon || '',
            alamat: item.alamat || '',
            statusVerifikasi: item.statusVerifikasi || 'pending'
          };

          dataManager.addAlumni(alumniData);
          successCount++;

          // Small delay to show progress
          await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
          errors.push(`Baris ${i + 1}: Error - ${error}`);
          errorCount++;
        }
      }

      // Show results
      let message = `Import selesai!\n`;
      message += `Berhasil: ${successCount} data\n`;
      if (errorCount > 0) {
        message += `Error: ${errorCount} data\n\n`;
        if (errors.length > 0) {
          message += `Detail error:\n${errors.slice(0, 5).join('\n')}`;
          if (errors.length > 5) {
            message += `\n... dan ${errors.length - 5} error lainnya`;
          }
        }
      }

      alert(message);
      loadAlumni();
      setIsImportDialogOpen(false);
      setImportFile(null);

    } catch (error) {
      console.error('Import error:', error);
      alert('Gagal mengimport data. Pastikan format file sesuai template.');
    } finally {
      setIsImporting(false);
      setImportProgress(0);
    }
  };

  // Helper functions
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });
  };



  const parseCSV = (csvContent: string): any[] => {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    // Find the header line (skip metadata lines)
    let headerIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('namaLengkap') || lines[i].includes('nim')) {
        headerIndex = i;
        break;
      }
    }

    const headers = lines[headerIndex].split(',').map(h => h.replace(/"/g, '').trim());
    const data: any[] = [];

    for (let i = headerIndex + 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
      if (values.length === headers.length) {
        const row: any = {};
        headers.forEach((header, index) => {
          row[header] = values[index];
        });
        data.push(row);
      }
    }

    return data;
  };

  const filteredAlumni = alumni.filter(item => {
    const matchesSearch = (item.namaLengkap || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.nim || '').includes(searchTerm) ||
                         (item.programStudi || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.email || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' || item.statusVerifikasi === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-800">Terverifikasi</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Menunggu</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Ditolak</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Daftar Alumni</h1>
          <p className="text-gray-600">Kelola data alumni dan status verifikasi</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Import Excel
          </Button>
          <Button className="university-gradient" onClick={handleAddAlumni}>
            <Plus className="h-4 w-4 mr-2" />
            Tambah Alumni
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari berdasarkan nama, NIM, atau program studi..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Semua Status</option>
                <option value="verified">Terverifikasi</option>
                <option value="pending">Menunggu</option>
                <option value="rejected">Ditolak</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alumni Table */}
      <Card>
        <CardHeader>
          <CardTitle>Data Alumni ({filteredAlumni.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">NIM</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Nama Lengkap</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Program Studi</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Tahun Lulus</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {filteredAlumni.map((item) => (
                  <tr key={item.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4 text-sm">{item.nim}</td>
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{item.namaLengkap}</div>
                        <div className="text-sm text-gray-500">{item.email}</div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-sm">{item.programStudi}</td>
                    <td className="py-3 px-4 text-sm">{item.tahunLulus}</td>
                    <td className="py-3 px-4">{getStatusBadge(item.statusVerifikasi)}</td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewAlumni(item)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" title="Edit" onClick={() => handleEditAlumni(item)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          title="Hapus"
                          onClick={() => handleDeleteAlumni(item)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredAlumni.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              Tidak ada data alumni yang ditemukan
            </div>
          )}
        </CardContent>
      </Card>
      </div>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedAlumni ? 'Edit Alumni' : 'Tambah Alumni Baru'}</DialogTitle>
            <DialogDescription>
              {selectedAlumni ? 'Edit informasi data alumni' : 'Isi formulir untuk menambahkan alumni baru'}
            </DialogDescription>
          </DialogHeader>
          <AlumniForm
            alumni={selectedAlumni}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus alumni <strong>{selectedAlumni?.name}</strong>?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Alumni Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detail Alumni</DialogTitle>
            <DialogDescription>
              Lihat detail lengkap informasi alumni.
            </DialogDescription>
          </DialogHeader>
          {selectedAlumni && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">NIM</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.nim}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama Lengkap</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.namaLengkap}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">No. Telepon</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.noTelepon}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Program Studi</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.programStudi}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Fakultas</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.fakultas}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tahun Lulus</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.tahunLulus}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">IPK</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.ipk}</p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">Alamat</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedAlumni.alamat}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status Verifikasi</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedAlumni.statusVerifikasi)}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Dibuat</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedAlumni.createdAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                <Button onClick={() => {
                  setIsViewDialogOpen(false);
                  handleEditAlumni(selectedAlumni);
                }}>
                  Edit Data
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Import Data Alumni</DialogTitle>
            <DialogDescription>
              Upload file Excel atau CSV untuk mengimpor data alumni secara massal.
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Upload File</TabsTrigger>
              <TabsTrigger value="template">Download Template</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Pilih File untuk Import:</Label>
                <Input
                  type="file"
                  accept=".json,.csv,.xlsx,.xls"
                  onChange={handleFileUpload}
                  className="mt-2"
                  disabled={isImporting}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Format yang didukung: Excel (.xlsx, .xls), CSV, JSON (maksimal 10MB)
                </p>
              </div>

              {importFile && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium">{importFile.name}</p>
                      <p className="text-xs text-gray-500">
                        Ukuran: {(importFile.size / 1024).toFixed(2)} KB
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {isImporting && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress Import:</span>
                    <span>{importProgress}%</span>
                  </div>
                  <Progress value={importProgress} className="w-full" />
                </div>
              )}

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Petunjuk Import:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• File harus berisi kolom: namaLengkap, nim (wajib)</li>
                  <li>• Kolom opsional: programStudi, fakultas, tahunLulus, email, noTelepon, alamat</li>
                  <li>• NIM harus unik (tidak boleh duplikat)</li>
                  <li>• Download template untuk format yang benar</li>
                </ul>
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsImportDialogOpen(false)}
                  className="flex-1"
                  disabled={isImporting}
                >
                  Batal
                </Button>
                <Button
                  onClick={handleImport}
                  disabled={!importFile || isImporting}
                  className="flex-1 university-gradient"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {isImporting ? 'Importing...' : 'Import Data'}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="template" className="space-y-4">
              <div className="text-sm text-gray-600">
                <p className="mb-4">Download template Excel untuk memudahkan import data alumni:</p>

                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h4 className="font-medium mb-2">Template berisi kolom:</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>• namaLengkap (wajib)</div>
                    <div>• nim (wajib)</div>
                    <div>• programStudi</div>
                    <div>• fakultas</div>
                    <div>• tahunLulus</div>
                    <div>• email</div>
                    <div>• noTelepon</div>
                    <div>• alamat</div>
                    <div>• statusVerifikasi</div>
                  </div>
                </div>

                <Button
                  onClick={downloadTemplate}
                  className="w-full university-gradient"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Template Alumni.xlsx
                </Button>

                <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                  <p className="text-xs text-yellow-800">
                    <strong>Catatan:</strong> Template berisi contoh data yang dapat Anda edit.
                    Pastikan format data sesuai dengan template sebelum mengimport.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default AlumniList;
