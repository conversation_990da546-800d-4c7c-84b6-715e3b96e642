import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Building, MapPin, Calendar, Edit, Trash2, Eye, User, DollarSign, TrendingUp, Target } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Layout from '@/components/Layout';
import EmploymentForm from '@/components/forms/EmploymentForm';
import { dataManager, EmploymentData, AlumniData } from '@/utils/dataManager';
import { sqliteDataManager } from '@/utils/sqliteDataManager';
import { useAuth } from '@/contexts/AuthContext';

const Employment = () => {
  const { user } = useAuth();
  const [employment, setEmployment] = useState<EmploymentData[]>([]);
  const [alumni, setAlumni] = useState<AlumniData[]>([]);
  const [filteredEmployment, setFilteredEmployment] = useState<EmploymentData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedEmployment, setSelectedEmployment] = useState<EmploymentData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAlumni, setCurrentAlumni] = useState<AlumniData | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterEmployment();
  }, [employment, searchTerm, filterType]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Check if server is available, use SQLite if available, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let employmentData, alumniData;

      if (isServerAvailable) {
        employmentData = await sqliteDataManager.getEmploymentData();
        alumniData = await sqliteDataManager.getAlumniData();
      } else {
        employmentData = dataManager.getEmploymentData();
        alumniData = dataManager.getAlumniData();
      }

      // For alumni users, only show their own employment data
      if (user?.role === 'alumni') {
        // Find the current alumni record
        const userAlumni = alumniData.find(a =>
          a.email === user.email || a.userId === user.id
        );

        setCurrentAlumni(userAlumni || null);

        if (userAlumni) {
          // Filter employment data to only show this alumni's records
          const filteredData = employmentData.filter(e => e.alumniId === userAlumni.id);
          setEmployment(filteredData);
        } else {
          setEmployment([]);
        }
      } else {
        // For admin/staff, show all employment data
        setEmployment(employmentData);
      }

      setAlumni(alumniData);
    } catch (error) {
      console.error('Error loading employment data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterEmployment = () => {
    let filtered = employment;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item => {
        const alumnus = alumni.find(a => a.id === item.alumniId);
        return (
          (item.namaPerusahaan || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.posisiJabatan || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.jenisUsaha || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (alumnus && (alumnus.namaLengkap || '').toLowerCase().includes(searchTerm.toLowerCase()))
        );
      });
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.statusPekerjaan === filterType);
    }

    setFilteredEmployment(filtered);
  };

  const handleAddEmployment = () => {
    setSelectedEmployment(null);
    setIsFormOpen(true);
  };

  const handleViewEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsViewDialogOpen(true);
  };

  const handleEditEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsFormOpen(true);
  };

  const handleDeleteEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (formData: any) => {
    setIsLoading(true);
    try {
      // Map form data to EmploymentData interface
      const employmentData: Omit<EmploymentData, 'id'> = {
        alumniId: formData.alumniId || '',
        namaPerusahaan: formData.companyName || '',
        posisiJabatan: formData.position || '',
        jenisUsaha: formData.industry || '',
        gajiPertama: parseInt(formData.salary) || 0,
        gajiSaatIni: parseInt(formData.salary) || 0,
        tanggalMulaiKerja: formData.startDate || '',
        statusPekerjaan: formData.isCurrentJob ? 'bekerja' : 'tidak_bekerja',
        relevansiPekerjaan: 'relevan' as 'sangat_relevan' | 'relevan' | 'kurang_relevan' | 'tidak_relevan'
      };

      // Check if server is available, use SQLite if available, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();

      if (selectedEmployment) {
        // Update existing employment
        if (isServerAvailable) {
          await sqliteDataManager.updateEmployment(selectedEmployment.id, employmentData);
        } else {
          dataManager.updateEmployment(selectedEmployment.id, employmentData);
        }
      } else {
        // Add new employment
        if (isServerAvailable) {
          await sqliteDataManager.addEmployment(employmentData);
        } else {
          dataManager.addEmployment(employmentData);
        }
      }
      loadData();
      setIsFormOpen(false);
      setSelectedEmployment(null);
    } catch (error) {
      console.error('Error saving employment:', error);
      alert('Gagal menyimpan data pekerjaan');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedEmployment) {
      setIsLoading(true);
      try {
        // Check if server is available, use SQLite if available, fallback to localStorage
        const isServerAvailable = await sqliteDataManager.isServerAvailable();

        if (isServerAvailable) {
          await sqliteDataManager.deleteEmployment(selectedEmployment.id);
        } else {
          dataManager.deleteEmployment(selectedEmployment.id);
        }

        loadData();
        setIsDeleteDialogOpen(false);
        setSelectedEmployment(null);
      } catch (error) {
        console.error('Error deleting employment:', error);
        alert('Gagal menghapus data pekerjaan');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getEmploymentTypeBadge = (status: string) => {
    const statusMap = {
      bekerja: { label: 'Bekerja', variant: 'default' as const },
      tidak_bekerja: { label: 'Tidak Bekerja', variant: 'secondary' as const },
      wirausaha: { label: 'Wirausaha', variant: 'outline' as const }
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const };
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short'
    });
  };

  const getAlumniName = (alumniId: string) => {
    const alumnus = alumni.find(a => a.id === alumniId);
    return alumnus ? alumnus.namaLengkap : 'Alumni tidak ditemukan';
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {user?.role === 'alumni' ? 'Data Pekerjaan Saya' : 'Data Pekerjaan Alumni'}
            </h1>
            <p className="text-gray-600">
              {user?.role === 'alumni'
                ? 'Kelola dan perbarui informasi pekerjaan Anda'
                : 'Kelola data pekerjaan dan karir alumni'
              }
            </p>
          </div>
          {user?.role !== 'admin' && (
            <Button className="university-gradient" onClick={handleAddEmployment}>
              <Plus className="h-4 w-4 mr-2" />
              {user?.role === 'alumni' ? 'Tambah Pekerjaan' : 'Tambah Data Pekerjaan'}
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Building className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Total Pekerjaan</p>
                  <p className="text-3xl font-bold text-gray-900">{employment.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-green-500 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-full">
                  <User className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Sedang Bekerja</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {employment.filter(e => e.statusPekerjaan === 'bekerja').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-purple-500 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Wirausaha</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {employment.filter(e => e.statusPekerjaan === 'wirausaha').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-l-orange-500 hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-orange-100 rounded-full">
                  <Building className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">Perusahaan Unik</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {new Set(employment.map(e => e.namaPerusahaan)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari berdasarkan nama alumni, perusahaan, posisi, atau industri..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="bekerja">Bekerja</SelectItem>
                    <SelectItem value="tidak_bekerja">Tidak Bekerja</SelectItem>
                    <SelectItem value="wirausaha">Wirausaha</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Employment List */}
        <Card>
          <CardHeader>
            <CardTitle>Data Pekerjaan ({filteredEmployment.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredEmployment.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <div className="p-6 bg-gray-100 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                  <Building className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {searchTerm || filterType !== 'all' ? 'Tidak ada data yang sesuai' : 'Belum ada data pekerjaan'}
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchTerm || filterType !== 'all'
                    ? 'Coba ubah kata kunci pencarian atau filter untuk melihat hasil lainnya.'
                    : user?.role === 'alumni'
                      ? 'Mulai dengan menambahkan informasi pekerjaan pertama Anda.'
                      : 'Belum ada data pekerjaan alumni yang tersedia di sistem.'
                  }
                </p>
                {(!searchTerm && filterType === 'all' && user?.role !== 'admin') && (
                  <Button className="university-gradient" onClick={handleAddEmployment}>
                    <Plus className="h-4 w-4 mr-2" />
                    {user?.role === 'alumni' ? 'Tambah Pekerjaan Saya' : 'Tambah Data Pekerjaan'}
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-3">
                {filteredEmployment.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Building className="h-4 w-4 text-blue-600" />
                        </div>

                        {/* Alumni Name (for admin/staff) */}
                        {user?.role !== 'alumni' && (
                          <div className="min-w-0 flex-1">
                            <p className="text-xs text-gray-500 uppercase tracking-wide">Alumni</p>
                            <p className="font-medium text-gray-900 truncate">{getAlumniName(item.alumniId)}</p>
                          </div>
                        )}

                        {/* Position & Company */}
                        <div className="min-w-0 flex-1">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Posisi</p>
                          <p className="font-medium text-gray-900 truncate">{item.posisiJabatan}</p>
                        </div>

                        <div className="min-w-0 flex-1">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Perusahaan</p>
                          <p className="font-medium text-gray-900 truncate">{item.namaPerusahaan}</p>
                        </div>

                        {/* Industry */}
                        <div className="min-w-0 flex-1">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Jenis Usaha</p>
                          <p className="font-medium text-gray-900 truncate">{item.jenisUsaha || 'Tidak diketahui'}</p>
                        </div>

                        {/* Status */}
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Status</p>
                          <div className="mt-1">
                            {getEmploymentTypeBadge(item.statusPekerjaan)}
                          </div>
                        </div>

                        {/* Salary */}
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Gaji Pertama</p>
                          <p className="font-medium text-gray-900">
                            Rp {(item.gajiPertama / 1000000).toFixed(1)} Jt
                          </p>
                        </div>

                        {/* Start Date */}
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Mulai Kerja</p>
                          <p className="font-medium text-gray-900">
                            {item.tanggalMulaiKerja ? new Date(item.tanggalMulaiKerja).toLocaleDateString('id-ID', {
                              month: 'short',
                              year: 'numeric'
                            }) : 'Tidak diketahui'}
                          </p>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 ml-4">
                        <Button variant="outline" size="sm" title="Lihat Detail" onClick={() => handleViewEmployment(item)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        {user?.role !== 'admin' && (
                          <>
                            <Button variant="outline" size="sm" title="Edit" onClick={() => handleEditEmployment(item)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700 hover:border-red-300"
                              title="Hapus"
                              onClick={() => handleDeleteEmployment(item)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedEmployment ? 'Edit Data Pekerjaan' : 'Tambah Data Pekerjaan Baru'}</DialogTitle>
            <DialogDescription>
              {selectedEmployment ? 'Edit informasi pekerjaan alumni' : 'Isi formulir untuk menambahkan data pekerjaan baru'}
            </DialogDescription>
          </DialogHeader>
          <EmploymentForm
            employment={selectedEmployment}
            alumni={alumni}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
            currentAlumni={currentAlumni}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus data pekerjaan <strong>{selectedEmployment?.position}</strong> di <strong>{selectedEmployment?.companyName}</strong>? 
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete} 
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Employment Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detail Pekerjaan Alumni</DialogTitle>
            <DialogDescription>
              Lihat detail lengkap informasi pekerjaan alumni.
            </DialogDescription>
          </DialogHeader>
          {selectedEmployment && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Alumni ID</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.alumniId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama Perusahaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.namaPerusahaan}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Posisi/Jabatan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.posisiJabatan}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Jenis Usaha</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.jenisUsaha}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Gaji Pertama</label>
                  <p className="mt-1 text-sm text-gray-900">
                    Rp {selectedEmployment.gajiPertama?.toLocaleString('id-ID') || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Gaji Saat Ini</label>
                  <p className="mt-1 text-sm text-gray-900">
                    Rp {selectedEmployment.gajiSaatIni?.toLocaleString('id-ID') || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Mulai Kerja</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedEmployment.tanggalMulaiKerja ?
                      new Date(selectedEmployment.tanggalMulaiKerja).toLocaleDateString('id-ID') : '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status Pekerjaan</label>
                  <div className="mt-1">
                    <Badge variant={
                      selectedEmployment.statusPekerjaan === 'bekerja' ? 'default' :
                      selectedEmployment.statusPekerjaan === 'wirausaha' ? 'secondary' : 'destructive'
                    }>
                      {selectedEmployment.statusPekerjaan === 'bekerja' ? 'Bekerja' :
                       selectedEmployment.statusPekerjaan === 'wirausaha' ? 'Wirausaha' : 'Tidak Bekerja'}
                    </Badge>
                  </div>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">Alamat Perusahaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.alamatPerusahaan || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Dibuat</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedEmployment.createdAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Terakhir Diupdate</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedEmployment.updatedAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                {user?.role !== 'admin' && (
                  <Button onClick={() => {
                    setIsViewDialogOpen(false);
                    handleEditEmployment(selectedEmployment);
                  }}>
                    Edit Data
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Employment;
