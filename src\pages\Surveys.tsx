import React, { useState, useEffect } from 'react';
import { Plus, Calendar, Users, BarChart3, Play, Pause, Eye, Edit, Trash2, FileText, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Layout from '@/components/Layout';
import SurveyForm from '@/components/forms/SurveyForm';
import SurveyResponseForm from '@/components/forms/SurveyResponseForm';
import { dataManager, SurveyData } from '@/utils/dataManager';
import { sqliteDataManager } from '@/utils/sqliteDataManager';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';

const Surveys = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [surveys, setSurveys] = useState<SurveyData[]>([]);
  const [surveyResponses, setSurveyResponses] = useState<any[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isResponseFormOpen, setIsResponseFormOpen] = useState(false);
  const [isResponsesDialogOpen, setIsResponsesDialogOpen] = useState(false);
  const [selectedSurvey, setSelectedSurvey] = useState<SurveyData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('surveys');

  useEffect(() => {
    loadSurveys();
    if (activeTab === 'responses') {
      loadSurveyResponses();
    }
  }, [activeTab]);

  const loadSurveys = async () => {
    try {
      setIsLoading(true);
      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let data;

      if (isServerAvailable) {
        data = await sqliteDataManager.getSurveyData();
      } else {
        data = dataManager.getSurveyData();
      }

      // For alumni users, only show active surveys
      if (user?.role === 'alumni') {
        const activeSurveys = data.filter(survey => survey.status === 'active');
        setSurveys(activeSurveys);
      } else {
        // For admin/staff, show all surveys
        setSurveys(data);
      }
    } catch (error) {
      console.error('Failed to load surveys:', error);
      // Fallback to localStorage
      const data = dataManager.getSurveyData();

      // Apply same role-based filtering for fallback data
      if (user?.role === 'alumni') {
        const activeSurveys = data.filter(survey => survey.status === 'active');
        setSurveys(activeSurveys);
      } else {
        setSurveys(data);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const loadSurveyResponses = async () => {
    try {
      setIsLoading(true);
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let allResponses: any[] = [];

      if (isServerAvailable) {
        // Load responses for all surveys
        for (const survey of surveys) {
          try {
            const response = await sqliteDataManager.getSurveyResponses(survey.id);
            if (response && response.length > 0) {
              const responsesWithSurveyInfo = response.map((resp: any) => ({
                ...resp,
                surveyTitle: survey.judul,
                surveyId: survey.id
              }));
              allResponses = [...allResponses, ...responsesWithSurveyInfo];
            }
          } catch (error) {
            console.error(`Failed to load responses for survey ${survey.id}:`, error);
          }
        }
      }

      setSurveyResponses(allResponses);
    } catch (error) {
      console.error('Failed to load survey responses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewSurveyResponses = async (survey: SurveyData) => {
    try {
      setSelectedSurvey(survey);
      setIsLoading(true);

      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      if (isServerAvailable) {
        const responses = await sqliteDataManager.getSurveyResponses(survey.id);
        setSurveyResponses(responses || []);
      }

      setIsResponsesDialogOpen(true);
    } catch (error) {
      console.error('Failed to load survey responses:', error);
      toast({
        title: "Error",
        description: "Gagal memuat hasil survey. Silakan coba lagi.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSurvey = () => {
    setSelectedSurvey(null);
    setIsFormOpen(true);
  };

  const handleViewSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsViewDialogOpen(true);
  };

  const handleEditSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsFormOpen(true);
  };

  const handleDeleteSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsDeleteDialogOpen(true);
  };

  const handleFillSurvey = (survey: SurveyData) => {
    setSelectedSurvey(survey);
    setIsResponseFormOpen(true);
  };

  const handleSurveyResponseSubmit = async (responses: { [questionId: string]: any }) => {
    if (!selectedSurvey || !user) return;

    try {
      setIsLoading(true);

      // Create survey response data
      const responseData = {
        surveyId: selectedSurvey.id,
        userId: user.id,
        responses: responses,
        submittedAt: new Date().toISOString()
      };

      // Save the response (this would typically go to a survey responses table)
      console.log('Survey response submitted:', responseData);

      toast({
        title: "Berhasil",
        description: "Jawaban survey berhasil dikirim. Terima kasih atas partisipasi Anda!",
      });

      setIsResponseFormOpen(false);
      setSelectedSurvey(null);
    } catch (error) {
      console.error('Error submitting survey response:', error);
      toast({
        title: "Error",
        description: "Gagal mengirim jawaban survey. Silakan coba lagi.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (formData: Omit<SurveyData, 'id' | 'createdAt' | 'updatedAt'>) => {
    setIsLoading(true);
    try {
      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();

      if (isServerAvailable) {
        if (selectedSurvey) {
          // Update existing survey
          await sqliteDataManager.updateSurvey(selectedSurvey.id, formData);
        } else {
          // Add new survey
          await sqliteDataManager.addSurvey(formData);
        }
      } else {
        if (selectedSurvey) {
          // Update existing survey
          dataManager.updateSurvey(selectedSurvey.id, formData);
        } else {
          // Add new survey
          dataManager.addSurvey(formData);
        }
      }

      loadSurveys();
      setIsFormOpen(false);
      setSelectedSurvey(null);
    } catch (error) {
      console.error('Error saving survey:', error);
      alert('Gagal menyimpan data survey');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedSurvey) {
      setIsLoading(true);
      try {
        // Try SQLite first, fallback to localStorage
        const isServerAvailable = await sqliteDataManager.isServerAvailable();

        if (isServerAvailable) {
          await sqliteDataManager.deleteSurvey(selectedSurvey.id);
        } else {
          dataManager.deleteSurvey(selectedSurvey.id);
        }

        loadSurveys();
        setIsDeleteDialogOpen(false);
        setSelectedSurvey(null);
      } catch (error) {
        console.error('Error deleting survey:', error);
        alert('Gagal menghapus data survey');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Tidak Aktif</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const toggleSurveyStatus = (surveyId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    // Update survey status logic here
    console.log(`Toggle survey ${surveyId} to ${newStatus}`);
  };

  const getResponseCount = (survey: SurveyData) => {
    return survey.responses ? survey.responses.length : 0;
  };

  const getTargetCount = (survey: SurveyData) => {
    return survey.targetAlumni ? survey.targetAlumni.length : 0;
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '') {
      return 'Belum diatur';
    }
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Tanggal tidak valid';
      }
      return date.toLocaleDateString('id-ID');
    } catch (error) {
      return 'Tanggal tidak valid';
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {user?.role === 'alumni' ? 'Survey & Kuesioner' : 'Manajemen Survey & Kuesioner'}
          </h1>
          <p className="text-gray-600">
            {user?.role === 'alumni'
              ? 'Isi survey dan kuesioner yang tersedia untuk alumni'
              : 'Kelola survey dan kuesioner untuk alumni'
            }
          </p>
        </div>
        {user?.role !== 'alumni' && (
          <Button className="university-gradient" onClick={handleAddSurvey}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Survey Baru
          </Button>
        )}
      </div>

      {/* Tabs for Admin/Staff users */}
      {user?.role !== 'alumni' ? (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="surveys" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Topik Survey</span>
            </TabsTrigger>
            <TabsTrigger value="responses" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Daftar Hasil Survey</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="surveys" className="space-y-6">
            {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Survey</p>
                <p className="text-2xl font-bold text-gray-900">{surveys.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Play className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Survey Aktif</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.filter(s => s.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Responden</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.reduce((total, survey) => total + getResponseCount(survey), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Survey Bulan Ini</p>
                <p className="text-2xl font-bold text-gray-900">
                  {surveys.filter(s => {
                    const surveyDate = new Date(s.tanggalMulai);
                    const currentDate = new Date();
                    return surveyDate.getMonth() === currentDate.getMonth() && 
                           surveyDate.getFullYear() === currentDate.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Survey List */}
      <Card>
        <CardHeader>
          <CardTitle>Daftar Survey</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Memuat data survey...</p>
            </div>
          ) : surveys.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>
                {user?.role === 'alumni'
                  ? 'Belum ada survey yang tersedia untuk diisi'
                  : 'Belum ada survey yang dibuat'
                }
              </p>
              {user?.role !== 'alumni' && (
                <Button className="mt-4 university-gradient" onClick={handleAddSurvey}>
                  <Plus className="h-4 w-4 mr-2" />
                  Buat Survey Pertama
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {surveys.map((survey) => (
                <div key={survey.id} className="border rounded-lg p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{survey.judul}</h3>
                        {getStatusBadge(survey.status)}
                      </div>
                      <p className="text-gray-600 mb-4">{survey.deskripsi}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Tanggal Mulai:</span>
                          <p className="font-medium">{formatDate(survey.tanggalMulai)}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Tanggal Selesai:</span>
                          <p className="font-medium">{formatDate(survey.tanggalSelesai)}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Target Alumni:</span>
                          <p className="font-medium">{getTargetCount(survey)} orang</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Responden:</span>
                          <p className="font-medium">{getResponseCount(survey)} orang</p>
                        </div>
                      </div>
                      
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <span>Progress:</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-xs">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ 
                                width: `${getTargetCount(survey) > 0 ? (getResponseCount(survey) / getTargetCount(survey)) * 100 : 0}%` 
                              }}
                            ></div>
                          </div>
                          <span>
                            {getTargetCount(survey) > 0 ? 
                              Math.round((getResponseCount(survey) / getTargetCount(survey)) * 100) : 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleSurveyStatus(survey.id, survey.status)}
                      >
                        {survey.status === 'active' ? (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Aktifkan
                          </>
                        )}
                      </Button>
                      
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewSurvey(survey)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        {user?.role === 'alumni' ? (
                          // For alumni users, show "Fill Survey" button
                          <Button
                            variant="default"
                            size="sm"
                            className="university-gradient"
                            title="Isi Survey"
                            onClick={() => handleFillSurvey(survey)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Isi Survey
                          </Button>
                        ) : (
                          // For admin/staff users, show edit and delete buttons
                          <>
                            <Button variant="ghost" size="sm" title="Edit" onClick={() => handleEditSurvey(survey)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              title="Hapus"
                              onClick={() => handleDeleteSurvey(survey)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
          </TabsContent>

          <TabsContent value="responses" className="space-y-6">
            {/* Survey Results Content */}
            <Card>
              <CardHeader>
                <CardTitle>Daftar Hasil Survey</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Memuat hasil survey...</p>
                  </div>
                ) : surveys.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Belum ada survey yang dibuat</p>
                    <Button className="mt-4 university-gradient" onClick={handleAddSurvey}>
                      <Plus className="h-4 w-4 mr-2" />
                      Buat Survey Pertama
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {surveys.map((survey) => (
                      <Card key={survey.id} className="hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="text-lg font-semibold text-gray-900">{survey.judul}</h3>
                                {getStatusBadge(survey.status)}
                              </div>
                              <p className="text-gray-600 mb-4">{survey.deskripsi}</p>

                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                  <span className="font-medium text-gray-700">Tanggal Mulai:</span>
                                  <p className="text-gray-600">{formatDate(survey.tanggalMulai)}</p>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">Tanggal Selesai:</span>
                                  <p className="text-gray-600">{formatDate(survey.tanggalSelesai)}</p>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">Responden:</span>
                                  <p className="text-gray-600">{getResponseCount(survey)} orang</p>
                                </div>
                              </div>
                            </div>

                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                title="Lihat Hasil Survey"
                                onClick={() => handleViewSurveyResponses(survey)}
                              >
                                <TrendingUp className="h-4 w-4 mr-1" />
                                Lihat Hasil
                              </Button>
                              <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewSurvey(survey)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        // Alumni view - no tabs, just survey list
        <div className="space-y-6">
          {/* Stats Cards for Alumni */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <BarChart3 className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Survey</p>
                    <p className="text-2xl font-bold text-gray-900">{surveys.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Play className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Survey Aktif</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {surveys.filter(s => s.status === 'active').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Belum Diisi</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {surveys.filter(s => !getResponseCount(s)).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Sudah Diisi</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {surveys.filter(s => getResponseCount(s) > 0).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Survey List for Alumni */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Survey & Kuesioner</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p>Memuat data survey...</p>
                </div>
              ) : surveys.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada survey yang tersedia untuk diisi</p>
                  <p className="text-sm text-gray-500 mt-2">Survey akan muncul di sini ketika admin membuat survey baru</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {surveys.map((survey) => (
                    <div key={survey.id} className="border rounded-lg p-6 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">{survey.judul}</h3>
                            {getStatusBadge(survey.status)}
                          </div>
                          <p className="text-gray-600 mb-4">{survey.deskripsi}</p>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Tanggal Mulai:</span>
                              <p className="text-gray-600">{formatDate(survey.tanggalMulai)}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Tanggal Selesai:</span>
                              <p className="text-gray-600">{formatDate(survey.tanggalSelesai)}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Jumlah Pertanyaan:</span>
                              <p className="text-gray-600">{survey.questions?.length || 0} pertanyaan</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" title="Lihat Detail" onClick={() => handleViewSurvey(survey)}>
                            <Eye className="h-4 w-4 mr-1" />
                            Detail
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="university-gradient"
                            title="Isi Survey"
                            onClick={() => handleFillSurvey(survey)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Isi Survey
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      </div>

      {/* Survey Responses Dialog */}
      <Dialog open={isResponsesDialogOpen} onOpenChange={setIsResponsesDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Hasil Survey: {selectedSurvey?.judul}</DialogTitle>
            <DialogDescription>
              Daftar respons yang telah dikumpulkan dari survey ini
            </DialogDescription>
          </DialogHeader>
          {selectedSurvey && (
            <div className="space-y-6">
              {surveyResponses.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada respons untuk survey ini</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Ringkasan</h4>
                    <p className="text-sm text-gray-600">
                      Total Responden: <span className="font-medium">{surveyResponses.length}</span>
                    </p>
                  </div>

                  {surveyResponses.map((response, index) => (
                    <Card key={response.id || index} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <h5 className="font-medium">Responden #{index + 1}</h5>
                          <span className="text-xs text-gray-500">
                            {new Date(response.submitted_at).toLocaleDateString('id-ID')}
                          </span>
                        </div>
                        <div className="space-y-2">
                          {Object.entries(response.responses || {}).map(([questionId, answer]) => (
                            <div key={questionId} className="border-b border-gray-100 pb-2">
                              <p className="text-sm font-medium text-gray-700">Pertanyaan {questionId}:</p>
                              <p className="text-sm text-gray-600">{String(answer)}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedSurvey ? 'Edit Survey' : 'Buat Survey Baru'}</DialogTitle>
            <DialogDescription>
              {selectedSurvey ? 'Edit informasi dan pertanyaan survey' : 'Isi formulir untuk membuat survey baru'}
            </DialogDescription>
          </DialogHeader>
          <SurveyForm
            survey={selectedSurvey}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus survey <strong>{selectedSurvey?.title}</strong>?
              Semua data respons akan ikut terhapus. Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Survey Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Detail Survey</DialogTitle>
            <DialogDescription>
              Lihat detail lengkap survey termasuk pertanyaan dan pengaturan.
            </DialogDescription>
          </DialogHeader>
          {selectedSurvey && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Judul Survey</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="mt-1">
                    <Badge variant={selectedSurvey.status === 'active' ? 'default' : 'secondary'}>
                      {selectedSurvey.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Mulai</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDate(selectedSurvey.tanggalMulai)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Selesai</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDate(selectedSurvey.tanggalSelesai)}
                  </p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">Deskripsi</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Target Alumni</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.targetAlumni?.length || 0} alumni</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Total Pertanyaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedSurvey.questions?.length || 0} pertanyaan</p>
                </div>
              </div>

              {/* Questions Preview */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-3 block">Pertanyaan Survey</label>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {(selectedSurvey.questions || []).map((question, index) => (
                    <div key={question.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <p className="text-sm font-medium">
                          {index + 1}. {question.question}
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {question.type}
                        </Badge>
                      </div>
                      {question.options && question.options.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-500 mb-1">Pilihan:</p>
                          <ul className="text-xs text-gray-600 list-disc list-inside">
                            {question.options.map((option, optIndex) => (
                              <li key={optIndex}>{option}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {question.required && (
                        <Badge variant="destructive" className="text-xs mt-2">
                          Wajib
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                {user?.role === 'alumni' ? (
                  // For alumni users, show "Fill Survey" button
                  <Button
                    className="university-gradient"
                    onClick={() => {
                      setIsViewDialogOpen(false);
                      handleFillSurvey(selectedSurvey!);
                    }}
                  >
                    Isi Survey
                  </Button>
                ) : (
                  // For admin/staff, show "Edit Survey" button
                  <Button onClick={() => {
                    setIsViewDialogOpen(false);
                    handleEditSurvey(selectedSurvey!);
                  }}>
                    Edit Survey
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Survey Response Form Dialog */}
      <Dialog open={isResponseFormOpen} onOpenChange={setIsResponseFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Isi Survey</DialogTitle>
            <DialogDescription>
              Silakan isi semua pertanyaan dalam survey ini dengan lengkap dan jujur.
            </DialogDescription>
          </DialogHeader>
          {selectedSurvey && (
            <SurveyResponseForm
              survey={selectedSurvey}
              onSubmit={handleSurveyResponseSubmit}
              onCancel={() => {
                setIsResponseFormOpen(false);
                setSelectedSurvey(null);
              }}
              isLoading={isLoading}
            />
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Surveys;
